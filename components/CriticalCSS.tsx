'use client';

import { useEffect } from 'react';

/**
 * مكون لتحميل CSS الحرج أولاً وتأجيل الباقي
 */
const CriticalCSS: React.FC = () => {
  useEffect(() => {
    // تحميل CSS غير الحرج بعد تحميل الصفحة
    const loadNonCriticalCSS = () => {
      // تحميل RemixIcon بشكل غير متزامن إذا لم يتم تحميله
      const remixIconLink = document.querySelector('link[href*="remixicon"]');
      if (!remixIconLink) {
        const link = document.createElement('link');
        link.rel = 'stylesheet';
        link.href = 'https://cdn.jsdelivr.net/npm/remixicon@3.5.0/fonts/remixicon.css';
        link.media = 'print';
        link.onload = function() {
          (this as HTMLLinkElement).media = 'all';
        };
        document.head.appendChild(link);
      }
    };

    // تحميل CSS غير الحرج بعد تحميل الصفحة
    if (document.readyState === 'complete') {
      loadNonCriticalCSS();
    } else {
      window.addEventListener('load', loadNonCriticalCSS);
    }

    return () => {
      window.removeEventListener('load', loadNonCriticalCSS);
    };
  }, []);

  return (
    <>
      {/* CSS حرج مضمن للتحميل الفوري */}
      <style jsx>{`
        /* CSS حرج للهيرو والتخطيط الأساسي */
        .hero-section {
          min-height: 100vh;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;
          overflow: hidden;
        }
        
        .hero-background {
          position: absolute;
          inset: 0;
          z-index: 0;
        }
        
        .hero-image {
          position: absolute;
          inset: 0;
          width: 100%;
          height: 100%;
          object-fit: cover;
          transition: opacity 1s ease-in-out;
        }
        
        .hero-overlay {
          position: absolute;
          inset: 0;
          background: linear-gradient(
            to right,
            rgba(0, 0, 0, 0.7),
            rgba(0, 0, 0, 0.5),
            rgba(0, 0, 0, 0.7)
          );
        }
        
        .hero-content {
          position: relative;
          z-index: 10;
          text-align: center;
          color: white;
          padding: 1rem;
          max-width: 80rem;
          margin: 0 auto;
        }
        
        .hero-title {
          font-size: 3rem;
          font-weight: bold;
          margin-bottom: 1.5rem;
          line-height: 1.2;
          animation: fadeInUp 0.8s ease-out forwards;
        }
        
        .hero-description {
          font-size: 1.25rem;
          margin-bottom: 2rem;
          opacity: 0.9;
          animation: fadeInUp 0.8s ease-out 0.2s forwards;
          animation-fill-mode: both;
        }
        
        .hero-buttons {
          display: flex;
          flex-direction: column;
          gap: 1rem;
          justify-content: center;
          animation: fadeInUp 0.8s ease-out 0.4s forwards;
          animation-fill-mode: both;
        }
        
        .hero-button {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          padding: 1rem 2rem;
          border-radius: 0.75rem;
          font-weight: bold;
          font-size: 1.125rem;
          transition: all 0.3s ease;
          text-decoration: none;
          transform: translateY(0);
        }
        
        .hero-button:hover {
          transform: translateY(-2px) scale(1.05);
          box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
        }
        
        .hero-button-primary {
          background: #2D3748;
          color: white;
        }
        
        .hero-button-primary:hover {
          background: rgba(45, 55, 72, 0.9);
        }
        
        .hero-button-whatsapp {
          background: #059669;
          color: white;
        }
        
        .hero-button-whatsapp:hover {
          background: #047857;
        }
        
        /* Animations */
        @keyframes fadeInUp {
          from {
            opacity: 0;
            transform: translateY(30px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
        
        /* Responsive */
        @media (min-width: 640px) {
          .hero-buttons {
            flex-direction: row;
          }
        }
        
        @media (min-width: 768px) {
          .hero-title {
            font-size: 4.5rem;
          }
        }
        
        @media (min-width: 1024px) {
          .hero-title {
            font-size: 6rem;
          }
        }
        
        /* تحسين الخطوط */
        .font-tajawal {
          font-family: var(--font-tajawal), 'Tajawal', sans-serif;
        }
        
        .font-poppins {
          font-family: var(--font-poppins), 'Poppins', sans-serif;
        }
        
        /* تحسين الاتجاه */
        .rtl {
          direction: rtl;
          text-align: right;
        }
        
        .ltr {
          direction: ltr;
          text-align: left;
        }
        
        /* تحسين التمرير */
        html {
          scroll-behavior: smooth;
        }
        
        body {
          font-family: var(--font-tajawal), 'Tajawal', sans-serif;
          direction: rtl;
          text-align: right;
        }
        
        /* تحسين الصور */
        img {
          max-width: 100%;
          height: auto;
        }
        
        /* تحسين الأزرار */
        button, .button {
          cursor: pointer;
          border: none;
          outline: none;
        }
        
        button:focus, .button:focus {
          box-shadow: 0 0 0 3px rgba(45, 55, 72, 0.3);
        }
        
        /* تحسين الروابط */
        a {
          color: inherit;
          text-decoration: none;
        }
        
        a:hover {
          text-decoration: none;
        }
        
        /* تحسين النماذج */
        input, textarea, select {
          font-family: inherit;
          font-size: inherit;
        }
        
        /* تحسين الأداء */
        * {
          box-sizing: border-box;
        }
        
        *::before,
        *::after {
          box-sizing: border-box;
        }
        
        /* تحسين التحميل */
        .loading {
          opacity: 0;
          animation: fadeIn 0.3s ease-in-out forwards;
        }
        
        @keyframes fadeIn {
          to {
            opacity: 1;
          }
        }
        
        /* تحسين الشبكة */
        .container {
          width: 100%;
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 1rem;
        }
        
        /* تحسين الفليكس */
        .flex {
          display: flex;
        }
        
        .flex-col {
          flex-direction: column;
        }
        
        .items-center {
          align-items: center;
        }
        
        .justify-center {
          justify-content: center;
        }
        
        .gap-4 {
          gap: 1rem;
        }
        
        /* تحسين النص */
        .text-center {
          text-align: center;
        }
        
        .text-white {
          color: white;
        }
        
        .font-bold {
          font-weight: bold;
        }
        
        /* تحسين الهوامش والحشو */
        .p-4 {
          padding: 1rem;
        }
        
        .mb-4 {
          margin-bottom: 1rem;
        }
        
        .mb-6 {
          margin-bottom: 1.5rem;
        }
        
        /* تحسين الخلفيات */
        .bg-primary {
          background-color: #2D3748;
        }
        
        .bg-green-600 {
          background-color: #059669;
        }
        
        /* تحسين الحدود */
        .rounded-xl {
          border-radius: 0.75rem;
        }
        
        /* تحسين الظلال */
        .shadow-lg {
          box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
        }
      `}</style>
    </>
  );
};

export default CriticalCSS;
