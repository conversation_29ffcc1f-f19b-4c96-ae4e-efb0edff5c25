<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الـ Favicon - دروب هجر</title>
    
    <!-- Favicon Links -->
    <link rel="icon" href="/favicon.ico" sizes="32x32" type="image/x-icon">
    <link rel="icon" href="/favicon.svg" type="image/svg+xml">
    <link rel="icon" type="image/png" sizes="16x16" href="/icons8-circled-d-ios-17-filled-16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="/icons8-circled-d-ios-17-filled-32.png">
    <link rel="apple-touch-icon" href="/apple-touch-icon.png" sizes="180x180">
    <link rel="manifest" href="/manifest.json">
    
    <!-- Meta Tags -->
    <meta name="description" content="اختبار عرض الـ favicon لموقع دروب هجر">
    <meta name="theme-color" content="#3B82F6">
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            direction: rtl;
            text-align: center;
            padding: 50px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            min-height: 100vh;
            margin: 0;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        h1 {
            font-size: 2.5em;
            margin-bottom: 30px;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        .favicon-list {
            text-align: right;
            margin: 30px 0;
        }
        .favicon-item {
            margin: 10px 0;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 10px;
        }
        .favicon-preview {
            display: inline-block;
            margin-left: 10px;
            vertical-align: middle;
        }
        a {
            color: #FFD700;
            text-decoration: none;
        }
        a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎯 اختبار الـ Favicon</h1>
        <p>هذه الصفحة لاختبار عرض أيقونة الموقع (Favicon) في المتصفح</p>
        
        <div class="favicon-list">
            <h3>ملفات الـ Favicon المتاحة:</h3>
            <div class="favicon-item">
                <span class="favicon-preview">🔗</span>
                <a href="/favicon.ico" target="_blank">favicon.ico</a> - الأيقونة الافتراضية
            </div>
            <div class="favicon-item">
                <span class="favicon-preview">🎨</span>
                <a href="/favicon.svg" target="_blank">favicon.svg</a> - أيقونة SVG قابلة للتكيف
            </div>
            <div class="favicon-item">
                <span class="favicon-preview">📱</span>
                <a href="/apple-touch-icon.png" target="_blank">apple-touch-icon.png</a> - أيقونة Apple
            </div>
            <div class="favicon-item">
                <span class="favicon-preview">📋</span>
                <a href="/manifest.json" target="_blank">manifest.json</a> - ملف البيان
            </div>
        </div>
        
        <p>تحقق من شريط العنوان في المتصفح لرؤية الأيقونة</p>
        <p><a href="/">العودة للصفحة الرئيسية</a></p>
    </div>
</body>
</html>
